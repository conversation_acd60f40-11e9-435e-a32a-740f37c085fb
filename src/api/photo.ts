import { http } from '@/http/request/alova'

/**
 * 证件照数据项
 */
export interface IPhotoItem {
  id: number
  name: string
  widthPx: number
  heightPx: number
  widthMm: number
  heightMm: number
  icon: number
  sort: number
  category: number
  dpi: number
}

/**
 * 分页请求参数
 */
export interface IPhotoListParams {
  pageNum: number
  pageSize: number
  type: number // 分类类型：1-常用尺寸，2-各类证件，3-各类签证
}

/**
 * 分页响应数据
 */
export interface IPhotoListResponse {
  records: IPhotoItem[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 获取证件照列表
 * @param params 分页参数
 * @returns Promise<IPhotoListResponse>
 */
export function getPhotoList(params: IPhotoListParams) {
  return http.Get<IPhotoListResponse>('/item/itemList', {
    params,
    meta: {
      ignoreAuth: true, // 忽略认证，不需要登录即可访问
    },
  })
}
